// ==UserScript==
// @name         Aria Downloader v5.0.0 - Linus Edition
// @namespace    aria-downloader.io
// @version      5.0.0
// @description  极简媒体捕获工具 - 消除特殊情况，回归本质
// <AUTHOR> Torvalds Philosophy Applied
// @match        *://*.youtube.com/*
// @match        *://*.bilibili.com/*
// @match        *://*.vimeo.com/*
// @match        *://*.netflix.com/*
// @match        *://*.twitch.tv/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

/*
 * Aria Downloader v5.0.0 - Linus Edition
 *
 * "Good taste" 重构原则：
 * 1. 消除所有特殊情况 - 统一的数据流
 * 2. 简化数据结构 - MediaSource -> Chunks -> Download
 * 3. 零依赖设计 - 不依赖外部库或复杂框架
 * 4. 单一职责 - 每个函数只做一件事
 *
 * 核心架构：
 * - MediaCapture: 劫持 MediaSource，捕获数据块
 * - ChunkStore: 内存中的简单数组存储
 * - SimpleUI: 最小化的用户界面
 * - Downloader: 直接下载，无复杂状态管理
 */

(function() {
    'use strict';

    // 防止在 iframe 中执行
    if (window.self !== window.top) return;

    // 全局配置 - 简化到最少必要参数
    const CONFIG = {
        MAX_SPEED: 4.0,
        BUFFER_THRESHOLD: 30, // 秒
        MAX_CHUNKS: 10000,    // 防止内存溢出
        UI_UPDATE_INTERVAL: 1000
    };

    // 核心数据结构 - 消除复杂的状态管理
    const MediaCapture = {
        chunks: { video: [], audio: [] },
        sizes: { video: 0, audio: 0 },
        isCapturing: false,
        videoElement: null,
        originalMethods: {},

        // 劫持 MediaSource - 最简实现
        hijack() {
            if (this.isCapturing) return;

            const originalAddSourceBuffer = MediaSource.prototype.addSourceBuffer;
            this.originalMethods.addSourceBuffer = originalAddSourceBuffer;

            MediaSource.prototype.addSourceBuffer = function(mimeType) {
                const sourceBuffer = originalAddSourceBuffer.call(this, mimeType);
                const isVideo = mimeType.includes('video');
                const type = isVideo ? 'video' : 'audio';

                // 劫持 appendBuffer - 核心捕获逻辑
                const originalAppendBuffer = sourceBuffer.appendBuffer;
                sourceBuffer.appendBuffer = function(buffer) {
                    // 捕获数据块
                    MediaCapture.addChunk(type, buffer);
                    return originalAppendBuffer.call(this, buffer);
                };

                return sourceBuffer;
            };

            this.isCapturing = true;
            console.log('[Aria v5.0] MediaSource hijacked successfully');
        },

        // 添加数据块 - 无状态，无特殊情况
        addChunk(type, buffer) {
            if (this.chunks[type].length >= CONFIG.MAX_CHUNKS) {
                console.warn('[Aria v5.0] Max chunks reached, stopping capture');
                return;
            }

            const chunk = new Uint8Array(buffer);
            this.chunks[type].push(chunk);
            this.sizes[type] += chunk.length;
        },

        // 获取完整数据 - 简单合并
        getData(type) {
            if (this.chunks[type].length === 0) return null;

            const totalSize = this.sizes[type];
            const result = new Uint8Array(totalSize);
            let offset = 0;

            for (const chunk of this.chunks[type]) {
                result.set(chunk, offset);
                offset += chunk.length;
            }

            return result;
        },

        // 清理数据
        clear() {
            this.chunks = { video: [], audio: [] };
            this.sizes = { video: 0, audio: 0 };
        },

        // 恢复原始方法
        restore() {
            if (this.originalMethods.addSourceBuffer) {
                MediaSource.prototype.addSourceBuffer = this.originalMethods.addSourceBuffer;
            }
            this.isCapturing = false;
        }
    };

    // 智能变速控制 - 消除复杂的状态机
    const SpeedControl = {
        isActive: false,
        animationId: null,

        start(videoElement) {
            if (this.isActive || !videoElement) return;

            this.isActive = true;
            MediaCapture.videoElement = videoElement;
            this.monitor(videoElement);
        },

        stop() {
            if (this.animationId) {
                cancelAnimationFrame(this.animationId);
                this.animationId = null;
            }
            this.isActive = false;

            // 恢复正常速度
            if (MediaCapture.videoElement) {
                MediaCapture.videoElement.playbackRate = 1.0;
            }
        },

        monitor(video) {
            if (!this.isActive || video.paused) {
                this.stop();
                return;
            }

            try {
                if (video.buffered.length > 0) {
                    const bufferEnd = video.buffered.end(video.buffered.length - 1);
                    const bufferAhead = bufferEnd - video.currentTime;

                    // 简单的线性映射 - 无复杂算法
                    let speed = 1.0;
                    if (bufferAhead > CONFIG.BUFFER_THRESHOLD) {
                        speed = Math.min(CONFIG.MAX_SPEED, 1.0 + (bufferAhead / CONFIG.BUFFER_THRESHOLD));
                    }

                    video.playbackRate = speed;
                }
            } catch (e) {
                // 静默处理错误，不中断流程
            }

            this.animationId = requestAnimationFrame(() => this.monitor(video));
        }
    };

    // 文件下载器 - 最简实现，无复杂状态
    const Downloader = {
        download(type) {
            const data = MediaCapture.getData(type);
            if (!data) {
                console.warn(`[Aria v5.0] No ${type} data to download`);
                return;
            }

            // 生成文件名 - 简单但有效
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            const title = this.getPageTitle();
            const filename = `${title}_${timestamp}_${type}.webm`;

            // 创建下载
            const blob = new Blob([data], { type: `${type}/webm` });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            // 延迟释放 URL
            setTimeout(() => URL.revokeObjectURL(url), 5000);

            console.log(`[Aria v5.0] Downloaded ${type}: ${filename}`);
        },

        getPageTitle() {
            // 通用标题提取 - 无站点特殊化
            let title = document.title;

            // 清理标题
            title = title.replace(/[\\/:*?"<>|]/g, '-');
            title = title.substring(0, 50);

            return title || 'video';
        }
    };

    // 极简UI - 消除复杂的Shadow DOM和状态管理
    const SimpleUI = {
        container: null,
        isVisible: false,
        updateTimer: null,

        create() {
            if (this.container) return;

            // 等待body可用 - 更强健的检查
            if (!document.body || document.readyState === 'loading') {
                console.log('[Aria v5.0] Waiting for DOM...');
                setTimeout(() => this.create(), 200);
                return;
            }

            // 创建浮动按钮
            const button = document.createElement('div');
            button.innerHTML = '📥';
            button.title = 'Aria v5.0 - 点击打开控制面板';
            button.style.cssText = `
                position: fixed !important;
                top: 20px !important;
                right: 20px !important;
                width: 50px !important;
                height: 50px !important;
                background: #007bff !important;
                color: white !important;
                border-radius: 50% !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                cursor: pointer !important;
                z-index: 2147483647 !important;
                font-size: 20px !important;
                box-shadow: 0 2px 10px rgba(0,0,0,0.3) !important;
                user-select: none !important;
                border: 2px solid white !important;
                transition: transform 0.2s !important;
            `;

            button.onmouseover = () => button.style.transform = 'scale(1.1)';
            button.onmouseout = () => button.style.transform = 'scale(1.0)';
            button.onclick = () => this.toggle();

            document.body.appendChild(button);
            console.log('[Aria v5.0] Floating button created and added to DOM');

            // 创建控制面板
            const panel = document.createElement('div');
            panel.style.cssText = `
                position: fixed !important;
                top: 80px !important;
                right: 20px !important;
                width: 300px !important;
                background: white !important;
                border: 2px solid #007bff !important;
                border-radius: 8px !important;
                padding: 15px !important;
                z-index: 2147483646 !important;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3) !important;
                font-family: Arial, sans-serif !important;
                font-size: 14px !important;
                color: #333 !important;
                display: none !important;
                max-height: 500px !important;
                overflow-y: auto !important;
            `;

            panel.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 10px;">Aria v5.0 - Linus Edition</div>
                <div id="aria-status">等待捕获...</div>
                <div style="margin: 10px 0;">
                    <div>视频: <span id="aria-video-size">0 MB</span></div>
                    <div>音频: <span id="aria-audio-size">0 MB</span></div>
                </div>
                <div style="margin: 10px 0;">
                    <label>
                        <input type="checkbox" id="aria-speed-toggle"> 智能变速
                    </label>
                    <input type="range" id="aria-max-speed" min="1" max="8" step="0.5" value="4" style="width: 100%; margin-top: 5px;">
                    <div style="text-align: center; font-size: 12px;">最高速度: <span id="aria-speed-value">4.0</span>x</div>
                </div>
                <div style="margin-top: 15px;">
                    <button id="aria-download-video" style="margin-right: 5px;">下载视频</button>
                    <button id="aria-download-audio">下载音频</button>
                </div>
                <div style="margin-top: 10px;">
                    <button id="aria-clear" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 4px;">清理数据</button>
                </div>
            `;

            document.body.appendChild(panel);
            this.container = { button, panel };

            console.log('[Aria v5.0] Control panel created and added to DOM');

            // 测试显示 - 3秒后自动显示面板确认UI工作
            setTimeout(() => {
                if (!this.isVisible) {
                    console.log('[Aria v5.0] Auto-showing panel for visibility test');
                    this.toggle();

                    // 5秒后自动隐藏
                    setTimeout(() => {
                        if (this.isVisible) {
                            this.toggle();
                            console.log('[Aria v5.0] Auto-hiding panel after test');
                        }
                    }, 5000);
                }
            }, 3000);

            this.bindEvents();
            this.startUpdates();
        },

        bindEvents() {
            const { panel } = this.container;

            // 智能变速开关
            const speedToggle = panel.querySelector('#aria-speed-toggle');
            const maxSpeedSlider = panel.querySelector('#aria-max-speed');
            const speedValue = panel.querySelector('#aria-speed-value');

            speedToggle.onchange = () => {
                if (speedToggle.checked && MediaCapture.videoElement) {
                    SpeedControl.start(MediaCapture.videoElement);
                } else {
                    SpeedControl.stop();
                }
            };

            maxSpeedSlider.oninput = () => {
                CONFIG.MAX_SPEED = parseFloat(maxSpeedSlider.value);
                speedValue.textContent = CONFIG.MAX_SPEED.toFixed(1);
            };

            // 下载按钮
            panel.querySelector('#aria-download-video').onclick = () => Downloader.download('video');
            panel.querySelector('#aria-download-audio').onclick = () => Downloader.download('audio');

            // 清理按钮
            panel.querySelector('#aria-clear').onclick = () => {
                MediaCapture.clear();
                console.log('[Aria v5.0] Data cleared');
            };
        },

        toggle() {
            if (!this.container) return;

            this.isVisible = !this.isVisible;
            this.container.panel.style.display = this.isVisible ? 'block' : 'none';
        },

        startUpdates() {
            if (this.updateTimer) return;

            this.updateTimer = setInterval(() => {
                this.updateStatus();
            }, CONFIG.UI_UPDATE_INTERVAL);
        },

        updateStatus() {
            if (!this.container || !this.isVisible) return;

            const { panel } = this.container;
            const videoSize = (MediaCapture.sizes.video / 1024 / 1024).toFixed(2);
            const audioSize = (MediaCapture.sizes.audio / 1024 / 1024).toFixed(2);

            panel.querySelector('#aria-video-size').textContent = `${videoSize} MB`;
            panel.querySelector('#aria-audio-size').textContent = `${audioSize} MB`;

            // 更新状态
            let status = '等待捕获...';
            if (MediaCapture.isCapturing) {
                const totalChunks = MediaCapture.chunks.video.length + MediaCapture.chunks.audio.length;
                status = `正在捕获 (${totalChunks} 块)`;
            }

            panel.querySelector('#aria-status').textContent = status;
        }
    };

    // 视频元素检测 - 简化逻辑
    const VideoDetector = {
        detected: false,

        start() {
            if (this.detected) return;

            // 立即检查
            this.checkForVideo();

            // 定期检查
            const checkInterval = setInterval(() => {
                if (this.checkForVideo()) {
                    clearInterval(checkInterval);
                }
            }, 1000);

            // 超时停止
            setTimeout(() => clearInterval(checkInterval), 30000);
        },

        checkForVideo() {
            const videos = document.querySelectorAll('video');

            for (const video of videos) {
                if (video.src || (video.srcObject && video.srcObject.activeSourceBuffers)) {
                    this.onVideoFound(video);
                    return true;
                }
            }

            return false;
        },

        onVideoFound(video) {
            if (this.detected) return;

            this.detected = true;
            MediaCapture.videoElement = video;

            console.log('[Aria v5.0] Video element detected');

            // 监听视频事件
            video.addEventListener('play', () => {
                console.log('[Aria v5.0] Video started playing');
            });

            video.addEventListener('ended', () => {
                console.log('[Aria v5.0] Video ended');
                SpeedControl.stop();
            });
        }
    };

    // 主初始化函数 - 分阶段启动
    function initialize() {
        console.log('[Aria v5.0] Initializing Linus Edition...');

        // 第一阶段：立即劫持 MediaSource（必须在document-start阶段）
        MediaCapture.hijack();

        // 第二阶段：等待DOM准备后创建UI
        initializeUI();

        // 第三阶段：检测视频
        VideoDetector.start();

        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            MediaCapture.restore();
            SpeedControl.stop();
        });

        console.log('[Aria v5.0] Core initialization complete');
    }

    // UI初始化 - 确保DOM可用
    function initializeUI() {
        function tryCreateUI() {
            if (document.body && document.readyState !== 'loading') {
                SimpleUI.create();
                console.log('[Aria v5.0] UI created successfully');
            } else {
                console.log('[Aria v5.0] DOM not ready, retrying UI creation...');
                setTimeout(tryCreateUI, 300);
            }
        }

        // 立即尝试
        tryCreateUI();

        // 同时监听DOM事件作为备份
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => SimpleUI.create(), 100);
            });
        }
    }

    // 启动 - 立即开始（MediaSource劫持必须尽早）
    initialize();

})();